package org.technoserve.udp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DTO for Dairy Output Report
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DairyOutputReportDto {
    private String partnerName;
    private String centreType;
    private String centreName;
    private String centreId;
    private Integer installedCapacity;
    private BigDecimal totalMilkVolume;
    private BigDecimal milkReceivedLPD;
    private BigDecimal utilizationPercentage;
    private BigDecimal averageFat;
    private BigDecimal averageSNF;
    private BigDecimal segregatedMilkVolume;
    private BigDecimal abPositiveMilkVolume;
    private BigDecimal afm1PositiveMilkVolume;
    private BigDecimal highSodiumMilkVolume;
    private BigDecimal overallPositiveMilkVolume;
    private BigDecimal overallNegativeMilkVolume;
    private BigDecimal qbiVolumeMilkVolume;
    private BigDecimal compliantPercentage;
    private BigDecimal abPosPercentage;
    private BigDecimal afm1PosPercentage;
    private BigDecimal betaPosVolume;
    private BigDecimal betaPosPercentage;
    private BigDecimal sulfaPosVolume;
    private BigDecimal sulfaPosPercentage;
    private BigDecimal tetraPosVolume;
    private BigDecimal tetraPosPercentage;
    private BigDecimal capPosVolume;
    private BigDecimal capPosPercentage;
    private BigDecimal aflaPosVolume;
    private BigDecimal aflaPosPercentage;
}
