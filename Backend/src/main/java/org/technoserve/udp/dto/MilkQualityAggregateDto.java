package org.technoserve.udp.dto;

import java.math.BigDecimal;

/**
 * Interface for aggregated milk quality data projection
 */
public interface MilkQualityAggregateDto {
    String getCentreId();
    Long getProgramId();
    Long getPartnerId();
    BigDecimal getTotalMilkVolume();
    BigDecimal getAvgFat();
    BigDecimal getAvgSnf();
    BigDecimal getSegregatedMilkVolume();
    BigDecimal getAbPositiveMilkVolume();
    BigDecimal getAfm1PositiveMilkVolume();
    BigDecimal getHighSodiumMilkVolume();
    BigDecimal getOverallPositiveMilkVolume();
    BigDecimal getOverallNegativeMilkVolume();
    BigDecimal getQbiVolumeMilkVolume();
    BigDecimal getBetaPosVolume();
    BigDecimal getSulfaPosVolume();
    BigDecimal getTetraPosVolume();
    BigDecimal getCapPosVolume();
    BigDecimal getAflaPosVolume();
    Long getRecordCount();
}
