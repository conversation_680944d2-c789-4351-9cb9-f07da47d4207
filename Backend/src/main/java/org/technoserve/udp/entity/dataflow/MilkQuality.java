package org.technoserve.udp.entity.dataflow;

import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.FieldInfo;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Entity representing Milk Quality data
 */
@Entity
@Table(name = "milk_quality")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MilkQuality {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "milk_quality_id")
    private Long milkQualityId;

    @Column(name = "date")
    @FieldInfo(name = "Date")
    private LocalDate date;

    @Column(name = "program_id")
    private Long programId;

    @Column(name = "partner_id")
    private Long partnerId;

    @Column(name = "centre_id")
    @FieldInfo(name = "Centre ID")
    private String centreId;

    @FieldInfo(name = "Total Milk Volume")
    @Column(name = "total_milk_volume")
    private BigDecimal totalMilkVolume;

    @FieldInfo(name = "FAT")
    @Column(name = "fat")
    private BigDecimal fat;

    @FieldInfo(name = "CLR")
    @Column(name = "clr")
    private BigDecimal clr;

    @FieldInfo(name = "SNF")
    @Column(name = "snf")
    private BigDecimal snf;

    @FieldInfo(name = "Segregated Milk Volume")
    @Column(name = "segregated_milk_volume")
    private BigDecimal segregatedMilkVolume;

    @FieldInfo(name = "AB Positive Milk Volume")
    @Column(name = "ab_positive_milk_volume")
    private BigDecimal abPositiveMilkVolume;

    @FieldInfo(name = "AFM1 Positive Milk Volume")
    @Column(name = "afm1_positive_milk_volume")
    private BigDecimal afm1PositiveMilkVolume;

    @FieldInfo(name = "High Sodium Milk Volume")
    @Column(name = "high_sodium_milk_volume")
    private BigDecimal highSodiumMilkVolume;

    @FieldInfo(name = "Overall Positive Milk Volume")
    @Column(name = "overall_positive_milk_volume")
    private BigDecimal overallPositiveMilkVolume;

    @FieldInfo(name = "Overall Negative Milk Volume")
    @Column(name = "overall_negative_milk_volume")
    private BigDecimal overallNegativeMilkVolume;

    @FieldInfo(name = "QBI Volume Milk Volume")
    @Column(name = "qbi_volume_milk_volume")
    private BigDecimal qbiVolumeMilkVolume;

    @FieldInfo(name = "Beta Pos Volume")
    @Column(name = "beta_pos_volume")
    private BigDecimal betaPosVolume;

    @FieldInfo(name = "Sulfa Pos Volume")
    @Column(name = "sulfa_pos_volume")
    private BigDecimal sulfaPosVolume;

    @FieldInfo(name = "Tetra Pos Volume")
    @Column(name = "tetra_pos_volume")
    private BigDecimal tetraPosVolume;

    @FieldInfo(name = "CAP Pos Volume")
    @Column(name = "cap_pos_volume")
    private BigDecimal capPosVolume;

    @FieldInfo(name = "Afla Pos Volume")
    @Column(name = "afla_pos_volume")
    private BigDecimal aflaPosVolume;

    @ManyToOne
    @JoinColumn(name = "excel_file_meta_data_id")
    private ExcelFileMetaData excelFileMetaData;
}
