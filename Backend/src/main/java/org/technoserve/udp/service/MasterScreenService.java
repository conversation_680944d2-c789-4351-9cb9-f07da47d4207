package org.technoserve.udp.service;

import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.MasterScreenDto;
import org.technoserve.udp.entity.screens.MasterScreen;
import org.technoserve.udp.repository.MasterScreenRepository;

import java.util.List;

/**
 * Service for handling master screen operations.
 */
@Slf4j // Enables logging
@Service
public class MasterScreenService {

    private final MasterScreenRepository masterScreenRepository;
    private final ModelMapper modelMapper;

    /**
     * Constructor injection for dependencies.
     *
     * @param masterScreenRepository Repository for MasterScreen entities.
     * @param modelMapper            ModelMapper for entity-to-DTO conversion.
     */
    public MasterScreenService(MasterScreenRepository masterScreenRepository, ModelMapper modelMapper) {
        this.masterScreenRepository = masterScreenRepository;
        this.modelMapper = modelMapper;
    }

    /**
     * Retrieves all master screens.
     *
     * @return List of MasterScreenDto objects.
     */
    public List<MasterScreenDto> getMasterData() {
        log.info("Fetching all master screens from the database...");

        List<MasterScreen> masterScreens = masterScreenRepository.findAll();

        log.info("Retrieved {} master screens from the database.", masterScreens.size());

        return masterScreens.stream()
                .map(screen -> modelMapper.map(screen, MasterScreenDto.class))
                .toList();
    }
}
