package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.CottonFarmingReportDto;
import org.technoserve.udp.dto.CottonFarmingYearDataDto;
import org.technoserve.udp.dto.DairyFieldDataDto;
import org.technoserve.udp.dto.DairyOutputReportDto;
import org.technoserve.udp.dto.MilkQualityAggregateDto;
import org.technoserve.udp.entity.dataflow.Centre;
import org.technoserve.udp.entity.dataflow.CottonFarmingData;
import org.technoserve.udp.entity.dataflow.DairyFieldData;
import org.technoserve.udp.entity.dataflow.Farmer;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.repository.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TransactionalReportService {

  private final MilkQualityRepository milkQualityRepository;
  private final CentreRepository centreRepository;
  private final PartnerRepository partnerRepository;
  private final ProgramRepository programRepository;
  private final DairyFieldDataRepository dairyFieldDataRepository;
  private final CottonFarmingDataRepository cottonFarmingDataRepository;
  private final FarmerRepository farmerRepository;

  private static final BigDecimal ONE_HUNDRED = new BigDecimal("100");

  /**
   * Generate dairy output report based on milk quality data with sorting and pagination
   *
   * @param partnerId The partner ID to filter by (optional)
   * @param programId The program ID to filter by (optional)
   * @param startDate The start date to filter by (optional)
   * @param endDate   The end date to filter by (optional)
   * @param sortBy    The field to sort by
   * @param sortDir   The sort direction (asc or desc)
   * @param page      The page number (0-based)
   * @param size      The page size
   * @return Map containing paginated and sorted dairy output report DTOs and metadata
   */
  public Map<String, Object> generateDairyOutputReport(Long partnerId, Long programId, LocalDate startDate,
                                                       LocalDate endDate, String sortBy, String sortDir, int page, int size) {
    // Set default date range if not provided
    if (startDate == null) startDate = LocalDate.now().minusYears(50);
    if (endDate == null) endDate = LocalDate.now();

    // Get aggregated milk quality data directly from the database using JPA grouping
    List<MilkQualityAggregateDto> aggregatedData = milkQualityRepository.findAggregatedByPartnerIdAndProgramIdAndDateBetween(
        partnerId, programId, startDate, endDate);

    List<DairyOutputReportDto> reportDtos = new ArrayList<>();

    // Process each aggregated record
    for (MilkQualityAggregateDto aggregate : aggregatedData) {
      String centreId = aggregate.getCentreId();
      Long aggProgramId = aggregate.getProgramId();
      Long aggPartnerId = aggregate.getPartnerId();

      // Get centre details
      Optional<Centre> centreOpt = centreRepository.findByCentreIdAndProgramIdAndPartnerId(
          centreId, aggProgramId, aggPartnerId);

      if (centreOpt.isEmpty()) {
        continue; // Skip if centre not found
      }

      Centre centre = centreOpt.get();

      // Get partner details
      Optional<Partner> partnerOpt = partnerRepository.findById(aggPartnerId);
      String partnerName = partnerOpt.map(Partner::getName).orElse("");

      // Calculate derived values
      BigDecimal totalMilkVolume = aggregate.getTotalMilkVolume() != null ? aggregate.getTotalMilkVolume() : BigDecimal.ZERO;
      BigDecimal recordCount = new BigDecimal(aggregate.getRecordCount());
      BigDecimal milkReceivedLPD = safeDivide(totalMilkVolume, recordCount, 2);

      // Calculate percentages
      BigDecimal utilizationPercentage = calculatePercentage(milkReceivedLPD,
          centre.getInstalledCapacity() != null ? new BigDecimal(centre.getInstalledCapacity()) : BigDecimal.ZERO);


      BigDecimal compliantPercentage = calculatePercentage(aggregate.getOverallNegativeMilkVolume(), aggregate.getTotalMilkVolume());
      BigDecimal abPosPercentage = calculatePercentage(aggregate.getAbPositiveMilkVolume(), aggregate.getTotalMilkVolume());
      BigDecimal afm1PosPercentage = calculatePercentage(aggregate.getAfm1PositiveMilkVolume(), aggregate.getTotalMilkVolume());
      BigDecimal betaPosPercentage = calculatePercentage(aggregate.getBetaPosVolume(), aggregate.getTotalMilkVolume());
      BigDecimal sulfaPosPercentage = calculatePercentage(aggregate.getSulfaPosVolume(), aggregate.getTotalMilkVolume());
      BigDecimal tetraPosPercentage = calculatePercentage(aggregate.getTetraPosVolume(), aggregate.getTotalMilkVolume());
      BigDecimal capPosPercentage = calculatePercentage(aggregate.getCapPosVolume(), aggregate.getTotalMilkVolume());
      BigDecimal aflaPosPercentage = calculatePercentage(aggregate.getAflaPosVolume(), aggregate.getTotalMilkVolume());

      // Create the report DTO
      DairyOutputReportDto reportDto = DairyOutputReportDto.builder()
          .partnerName(partnerName)
          .centreType(centre.getCentreType() != null ? centre.getCentreType().getType() : null)
          .centreName(centre.getCentreName())
          .centreId(centreId)
          .installedCapacity(centre.getInstalledCapacity())
          .totalMilkVolume(totalMilkVolume)
          .milkReceivedLPD(milkReceivedLPD)
          .utilizationPercentage(utilizationPercentage)
          .averageFat(aggregate.getAvgFat())
          .averageSNF(aggregate.getAvgSnf())
          .segregatedMilkVolume(aggregate.getSegregatedMilkVolume())
          .abPositiveMilkVolume(aggregate.getAbPositiveMilkVolume())
          .afm1PositiveMilkVolume(aggregate.getAfm1PositiveMilkVolume())
          .highSodiumMilkVolume(aggregate.getHighSodiumMilkVolume())
          .overallPositiveMilkVolume(aggregate.getOverallPositiveMilkVolume())
          .overallNegativeMilkVolume(aggregate.getOverallNegativeMilkVolume())
          .qbiVolumeMilkVolume(aggregate.getQbiVolumeMilkVolume())
          .compliantPercentage(compliantPercentage)
          .abPosPercentage(abPosPercentage)
          .afm1PosPercentage(afm1PosPercentage)
          .betaPosVolume(aggregate.getBetaPosVolume())
          .betaPosPercentage(betaPosPercentage)
          .sulfaPosVolume(aggregate.getSulfaPosVolume())
          .sulfaPosPercentage(sulfaPosPercentage)
          .tetraPosVolume(aggregate.getTetraPosVolume())
          .tetraPosPercentage(tetraPosPercentage)
          .capPosVolume(aggregate.getCapPosVolume())
          .capPosPercentage(capPosPercentage)
          .aflaPosVolume(aggregate.getAflaPosVolume())
          .aflaPosPercentage(aflaPosPercentage)
          .build();

      reportDtos.add(reportDto);
    }

    // Sort the report DTOs based on the specified field and direction
    Comparator<DairyOutputReportDto> comparator = getComparator(sortBy);
    if ("desc".equalsIgnoreCase(sortDir)) {
      comparator = comparator.reversed();
    }

    List<DairyOutputReportDto> sortedReportDtos = reportDtos.stream()
        .sorted(comparator)
        .toList();

    // Apply pagination
    int totalItems = sortedReportDtos.size();
    int totalPages = (int) Math.ceil((double) totalItems / size);

    // Ensure page is within bounds
    if (page >= totalPages && totalPages > 0) {
      page = totalPages - 1;
    }

    int fromIndex = page * size;
    int toIndex = Math.min(fromIndex + size, totalItems);

    List<DairyOutputReportDto> paginatedReportDtos = fromIndex < totalItems ?
        sortedReportDtos.subList(fromIndex, toIndex) : new ArrayList<>();

    // Create response with pagination metadata
    Map<String, Object> response = new HashMap<>();
    response.put("dairyOutputReport", paginatedReportDtos);
    response.put("currentPage", page);
    response.put("totalItems", totalItems);
    response.put("totalPages", totalPages);
    response.put("size", size);
    response.put("sortBy", sortBy);
    response.put("sortDir", sortDir);

    // Get the latest dairy field data for the given date range
    List<DairyFieldDataDto> latestFieldDataList = getLatestDairyFieldData(partnerId, programId, startDate, endDate);
    if (latestFieldDataList != null) {
      response.put("dairyFieldData", latestFieldDataList);
    }

    return response;
  }

  /**
   * Get a comparator for sorting DairyOutputReportDto objects by the specified field
   *
   * @param sortBy The field to sort by
   * @return A comparator for the specified field
   */
  private Comparator<DairyOutputReportDto> getComparator(String sortBy) {
    return switch (sortBy.toLowerCase()) {
      case "centreType" ->
          Comparator.comparing(DairyOutputReportDto::getCentreType, Comparator.nullsLast(String::compareTo));
      case "centreName" ->
          Comparator.comparing(DairyOutputReportDto::getCentreName, Comparator.nullsLast(String::compareTo));
      case "centreId" ->
          Comparator.comparing(DairyOutputReportDto::getCentreId, Comparator.nullsLast(String::compareTo));
      case "installedCapacity" ->
          Comparator.comparing(DairyOutputReportDto::getInstalledCapacity, Comparator.nullsLast(Integer::compareTo));
      case "totalmilkVolume" ->
          Comparator.comparing(DairyOutputReportDto::getTotalMilkVolume, Comparator.nullsLast(BigDecimal::compareTo));
      case "milkreceivedLpd" ->
          Comparator.comparing(DairyOutputReportDto::getMilkReceivedLPD, Comparator.nullsLast(BigDecimal::compareTo));
      case "utilizationPercentage" ->
          Comparator.comparing(DairyOutputReportDto::getUtilizationPercentage, Comparator.nullsLast(BigDecimal::compareTo));
      case "averagefat" ->
          Comparator.comparing(DairyOutputReportDto::getAverageFat, Comparator.nullsLast(BigDecimal::compareTo));
      case "averagesnf" ->
          Comparator.comparing(DairyOutputReportDto::getAverageSNF, Comparator.nullsLast(BigDecimal::compareTo));
      default -> Comparator.comparing(DairyOutputReportDto::getPartnerName, Comparator.nullsLast(String::compareTo));
    };
  }

  /**
   * Calculate percentage
   *
   * @param part  The part value
   * @param total The total value
   * @return The percentage (part/total * 100)
   */
  private BigDecimal calculatePercentage(BigDecimal part, BigDecimal total) {
    if (part == null) {
      return BigDecimal.ZERO;
    }

    return safeDivide(part.multiply(ONE_HUNDRED), total, 2);
  }

  /**
   * Safely divide two BigDecimal values, handling division by zero
   *
   * @param numerator   The numerator
   * @param denominator The denominator
   * @param scale       The scale of the result
   * @return The result of the division, or zero if denominator is zero
   */
  private BigDecimal safeDivide(BigDecimal numerator, BigDecimal denominator, int scale) {
    if (numerator == null) {
      return BigDecimal.ZERO;
    }

    if (denominator == null || denominator.compareTo(BigDecimal.ZERO) == 0) {
      return BigDecimal.ZERO;
    }

    return numerator.divide(denominator, scale, RoundingMode.HALF_UP);
  }

  /**
   * Get the latest dairy field data for the given partner, program, and date range
   *
   * @param partnerId The partner ID (optional)
   * @param programId The program ID (optional)
   * @param startDate The start date (optional)
   * @param endDate   The end date (optional)
   * @return The latest dairy field data DTO, or null if no data found
   */
  public List<DairyFieldDataDto> getLatestDairyFieldData(Long partnerId, Long programId, LocalDate startDate, LocalDate endDate) {
    // Fetch latest field data
    List<DairyFieldData> fieldDataList = dairyFieldDataRepository.findLatestByProgramIdAndPartnerIdAndDateRange(
        programId, partnerId, startDate, endDate);

    if (fieldDataList.isEmpty()) {
      return Collections.emptyList();
    }

    // Sort by date descending
    List<DairyFieldData> sortedList = fieldDataList.stream()
        .sorted(Comparator.comparing(DairyFieldData::getDate).reversed())
        .toList();

    // Group by (programId, partnerId) and get the latest per group
    List<DairyFieldData> latestPerGroup = sortedList.stream()
        .collect(Collectors.groupingBy(
            data -> Arrays.asList(data.getProgramId(), data.getPartnerId()),
            LinkedHashMap::new,
            Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))
        ))
        .values()
        .stream()
        .toList();

    // Map to DTOs
    return latestPerGroup.stream()
        .map(data -> {
          String partnerName = partnerRepository.findById(data.getPartnerId())
              .map(Partner::getName)
              .orElse("");

          String programName = programRepository.findById(data.getProgramId())
              .map(Program::getName)
              .orElse("");

          return DairyFieldDataDto.builder()
              .totalFarmers(data.getTotalFarmers())
              .noOfAnimalWelfareFarms(data.getNoOfAnimalWelfareFarms())
              .noOfWomenEmpowerment(data.getNoOfWomenEmpowerment())
              .womenEmpowermentPercentage(
                  Optional.ofNullable(data.getTotalFarmers())
                      .filter(total -> total > 0)
                      .map(total -> {
                        Integer empowered = Optional.ofNullable(data.getNoOfWomenEmpowerment()).orElse(0);
                        return (empowered * 100.0) / total;
                      })
                      .orElse(0.0)
              )
              .partnerName(partnerName)
              .programName(programName)
              .build();
        }).toList();
  }

  /**
   * Generate cotton farming report by joining Farmer and CottonFarmingData entities at database level
   *
   * @param partnerId The partner ID to filter by (optional)
   * @param programId The program ID to filter by (optional)
   * @param years     The list of years to filter by (optional)
   * @param sortBy    The field to sort by
   * @param sortDir   The sort direction (asc or desc)
   * @param page      The page number (0-based)
   * @param size      The page size
   * @return Map containing paginated and sorted cotton farming report DTOs and metadata
   */
  public Map<String, Object> generateCottonFarmingReport(Long partnerId, Long programId, List<Integer> years, String sortBy, String sortDir, int page, int size) {
    // Get joined data from database
    List<Object[]> joinedData = cottonFarmingDataRepository.findFarmersWithCottonData(programId, partnerId, years);

    // Group data by farmer
    Map<String, CottonFarmingReportDto> farmerMap = new LinkedHashMap<>();

    for (Object[] row : joinedData) {
      Farmer farmer = (Farmer) row[0];
      CottonFarmingData cottonData = (CottonFarmingData) row[1];

      String farmerKey = farmer.getFarmerId() + "_" + farmer.getProgramId() + "_" + farmer.getPartnerId();

      // Get or create farmer report DTO
      CottonFarmingReportDto reportDto = farmerMap.get(farmerKey);
      if (reportDto == null) {
        // Get centre details if available
        String centreName = null;
        String centreType = null;

        if (farmer.getCentreId() != null) {
          Optional<Centre> centreOpt = centreRepository.findByCentreIdAndProgramIdAndPartnerId(
              farmer.getCentreId(), farmer.getProgramId(), farmer.getPartnerId());

          if (centreOpt.isPresent()) {
            Centre centre = centreOpt.get();
            centreName = centre.getCentreName();
            centreType = centre.getCentreType() != null ? centre.getCentreType().getType() : null;
          }
        }

        // Get partner name
        String partnerName = "Unknown";
        Optional<Partner> partnerOpt = partnerRepository.findById(farmer.getPartnerId());
        if (partnerOpt.isPresent()) {
          partnerName = partnerOpt.get().getName();
        }

        // Get program name
        String programName = "Unknown";
        Optional<Program> programOpt = programRepository.findById(farmer.getProgramId());
        if (programOpt.isPresent()) {
          programName = programOpt.get().getName();
        }

        // Create new farmer report DTO
        reportDto = CottonFarmingReportDto.builder()
            .farmerId(farmer.getFarmerId())
            .farmerTracenetCode(farmer.getFarmerTracenetCode())
            .farmerName(farmer.getFarmerName())
            .age(farmer.getAge())
            .gender(farmer.getGender())
            .state(farmer.getState())
            .district(farmer.getDistrict())
            .village(farmer.getVillage())
            .mobileNumber(farmer.getMobileNumber())
            .centreId(farmer.getCentreId())
            .centreName(centreName)
            .centreType(centreType)
            .maritalStatus(farmer.getMaritalStatus())
            .spouseName(farmer.getSpouseName())
            .caste(farmer.getCaste())
            .highestEducation(farmer.getHighestEducation())
            .houseHoldSize(farmer.getHouseHoldSize())
            .landSizeUnderCultivation(farmer.getLandSizeUnderCultivation())
            .landMeasureType(farmer.getLandMeasureType())
            .organicStatus(farmer.getOrganicStatus())
            .herdSize(farmer.getHerdSize())
            .anyOtherIncomeGeneratingActivity(farmer.getAnyOtherIncomeGeneratingActivity())
            .householdAnnualIncome(farmer.getHouseholdAnnualIncome())
            .agriculturalAnnualIncome(farmer.getAgriculturalAnnualIncome())
            .dairyAnnualIncome(farmer.getDairyAnnualIncome())
            .otherAnnualIncome(farmer.getOtherAnnualIncome())
            .cropsGrown(farmer.getCropsGrown())
            .cattleBreedTypes(farmer.getCattleBreedTypes())
            .loanAmount(farmer.getLoanAmount())
            .agriculturalLoan(farmer.getAgriculturalLoan())
            .dairyLoan(farmer.getDairyLoan())
            .latLong(farmer.getLatLong())
            .partnerName(partnerName)
            .programName(programName)
            .yearData(new ArrayList<>())
            .build();

        farmerMap.put(farmerKey, reportDto);
      }

      // Add cotton farming data for this year if it exists
      if (cottonData != null) {
        CottonFarmingYearDataDto yearDataDto = createYearDataDto(cottonData);
        reportDto.getYearData().add(yearDataDto);
      }
    }

    // Convert to list and apply sorting
    List<CottonFarmingReportDto> reportDtos = new ArrayList<>(farmerMap.values());

    // Sort the list
    Comparator<CottonFarmingReportDto> comparator = getCottonFarmingComparator(sortBy);
    if ("desc".equalsIgnoreCase(sortDir)) {
      comparator = comparator.reversed();
    }
    reportDtos.sort(comparator);

    // Apply pagination
    int totalItems = reportDtos.size();
    int totalPages = (int) Math.ceil((double) totalItems / size);

    // Ensure page is within bounds
    if (page >= totalPages && totalPages > 0) {
      page = totalPages - 1;
    }

    int fromIndex = page * size;
    int toIndex = Math.min(fromIndex + size, totalItems);

    List<CottonFarmingReportDto> paginatedReportDtos = fromIndex < totalItems ?
        reportDtos.subList(fromIndex, toIndex) : new ArrayList<>();

    // Create response with pagination metadata
    Map<String, Object> response = new HashMap<>();
    response.put("cottonFarmingReport", paginatedReportDtos);
    response.put("currentPage", page);
    response.put("totalItems", totalItems);
    response.put("totalPages", totalPages);
    response.put("size", size);
    response.put("sortBy", sortBy);
    response.put("sortDir", sortDir);

    return response;
  }

  /**
   * Create a CottonFarmingYearDataDto from CottonFarmingData entity
   *
   * @param cottonData The cotton farming data entity
   * @return The year data DTO
   */
  private CottonFarmingYearDataDto createYearDataDto(CottonFarmingData cottonData) {
    return CottonFarmingYearDataDto.builder()
        .year(cottonData.getYear())
        .malesInHousehold(cottonData.getMalesInHousehold())
        .femalesInHousehold(cottonData.getFemalesInHousehold())
        .childrenInHousehold(cottonData.getChildrenInHousehold())
        .schoolGoingChildren(cottonData.getSchoolGoingChildren())
        .earningMembers(cottonData.getEarningMembers())
        .totalLandholding(cottonData.getTotalLandholding())
        .primaryCrop(cottonData.getPrimaryCrop())
        .secondaryCrops(cottonData.getSecondaryCrops())
        .nonOrganicCottonLand(cottonData.getNonOrganicCottonLand())
        .organicCottonLand(cottonData.getOrganicCottonLand())
        .yearsOrganicPractice(cottonData.getYearsOrganicPractice())
        .certificationStatus(cottonData.getCertificationStatus())
        .irrigationSource(cottonData.getIrrigationSource())
        .cattleCount(cottonData.getCattleCount())
        .drinkingWaterSource(cottonData.getDrinkingWaterSource())
        .preferredSellingPoint(cottonData.getPreferredSellingPoint())
        .hasStorageSpace(cottonData.getHasStorageSpace())
        .receivesAgroAdvisory(cottonData.getReceivesAgroAdvisory())
        .receivedTraining(cottonData.getReceivedTraining())
        .membershipInOrg(cottonData.getMembershipInOrg())
        .maintainsRecords(cottonData.getMaintainsRecords())
        .annualHouseholdIncome(cottonData.getAnnualHouseholdIncome())
        .primaryIncomeSource(cottonData.getPrimaryIncomeSource())
        .primaryIncomeAmount(cottonData.getPrimaryIncomeAmount())
        .certificationCostPerAcre(cottonData.getCertificationCostPerAcre())
        .avgProductionPerAcre(cottonData.getAvgProductionPerAcre())
        .costOfCultivationPerAcre(cottonData.getCostOfCultivationPerAcre())
        .organicCottonQuantitySold(cottonData.getOrganicCottonQuantitySold())
        .sellingPricePerKg(cottonData.getSellingPricePerKg())
        .bioInputsCost(cottonData.getBioInputsCost())
        .pestManagementBioInputs(cottonData.getPestManagementBioInputs())
        .bioFertilizerUsed(cottonData.getBioFertilizerUsed())
        .pheromoneTrapsPerAcre(cottonData.getPheromoneTrapsPerAcre())
        .yellowStickyTrapsPerAcre(cottonData.getYellowStickyTrapsPerAcre())
        .blueStickyTrapsPerAcre(cottonData.getBlueStickyTrapsPerAcre())
        .birdPerchesPerAcre(cottonData.getBirdPerchesPerAcre())
        .irrigationCostPerAcre(cottonData.getIrrigationCostPerAcre())
        .irrigationCount(cottonData.getIrrigationCount())
        .irrigationMethod(cottonData.getIrrigationMethod())
        .farmMachineryHired(cottonData.getFarmMachineryHired())
        .machineryHiringCost(cottonData.getMachineryHiringCost())
        .localLabourCostPerDay(cottonData.getLocalLabourCostPerDay())
        .migrantLabourCostPerDay(cottonData.getMigrantLabourCostPerDay())
        .workersForSowing(cottonData.getWorkersForSowing())
        .workersForHarvesting(cottonData.getWorkersForHarvesting())
        .harvestingTime(cottonData.getHarvestingTime())
        .weedingMethod(cottonData.getWeedingMethod())
        .weedingCostPerAcre(cottonData.getWeedingCostPerAcre())
        .mulchingCostPerAcre(cottonData.getMulchingCostPerAcre())
        .tillageCount(cottonData.getTillageCount())
        .tillageCostPerAcre(cottonData.getTillageCostPerAcre())
        .landPreparationCost(cottonData.getLandPreparationCost())
        .organicCottonSeedRate(cottonData.getOrganicCottonSeedRate())
        .organicCottonSeedVariety(cottonData.getOrganicCottonSeedVariety())
        .borderCrop(cottonData.getBorderCrop())
        .interCrop(cottonData.getInterCrop())
        .coverCrop(cottonData.getCoverCrop())
        .trapCrop(cottonData.getTrapCrop())
        .mulchingUsed(cottonData.getMulchingUsed())
        .mulchingType(cottonData.getMulchingType())
        .storagePrecautions(cottonData.getStoragePrecautions())
        .hiredVehicleForTransport(cottonData.getHiredVehicleForTransport())
        .transportationCostPerKg(cottonData.getTransportationCostPerKg())
        .rejectedQuantity(cottonData.getRejectedQuantity())
        .priceDiscoveryMechanism(cottonData.getPriceDiscoveryMechanism())
        .paymentTransactionType(cottonData.getPaymentTransactionType())
        .creditDays(cottonData.getCreditDays())
        .govtSchemeAvailed(cottonData.getGovtSchemeAvailed())
        .cropInsurance(cottonData.getCropInsurance())
        .cropInsuranceCostPerAcre(cottonData.getCropInsuranceCostPerAcre())
        .hasKCC(cottonData.getHasKCC())
        .hasActiveBankAccount(cottonData.getHasActiveBankAccount())
        .cropRotationUsed(cottonData.getCropRotationUsed())
        .rotationCrops(cottonData.getRotationCrops())
        .waterTrackingDevices(cottonData.getWaterTrackingDevices())
        .pumpCapacity(cottonData.getPumpCapacity())
        .bufferZone(cottonData.getBufferZone())
        .cropResidueUtilization(cottonData.getCropResidueUtilization())
        .workerPaymentMode(cottonData.getWorkerPaymentMode())
        .wageGenderDifference(cottonData.getWageGenderDifference())
        .labourRegister(cottonData.getLabourRegister())
        .safetyKitForWorkers(cottonData.getSafetyKitForWorkers())
        .shelterAndWaterForWorkers(cottonData.getShelterAndWaterForWorkers())
        .lavatoryForWorkers(cottonData.getLavatoryForWorkers())
        .womenInAgriOperations(cottonData.getWomenInAgriOperations())
        .communityWaterHarvesting(cottonData.getCommunityWaterHarvesting())
        .soilMoistureMeterUsed(cottonData.getSoilMoistureMeterUsed())
        // Calculated fields
        .totalHHMembers(cottonData.getTotalHHMembers())
        .dependencyRatio(cottonData.getDependencyRatio())
        .genderRatio(cottonData.getGenderRatio())
        .schoolAttendanceRate(cottonData.getSchoolAttendanceRate())
        .totalCottonLand(cottonData.getTotalCottonLand())
        .organicPercent(cottonData.getOrganicPercent())
        .landUsedForCotton(cottonData.getLandUsedForCotton())
        .incomePerEarner(cottonData.getIncomePerEarner())
        .ocIncome(cottonData.getOcIncome())
        .profitPerAcre(cottonData.getProfitPerAcre())
        .totalCertificationCost(cottonData.getTotalCertificationCost())
        .totalPTCost(cottonData.getTotalPTCost())
        .totalYSTCost(cottonData.getTotalYSTCost())
        .totalBSTCost(cottonData.getTotalBSTCost())
        .totalPestMgmtCost(cottonData.getTotalPestMgmtCost())
        .totalLabourCost(cottonData.getTotalLabourCost())
        .machineryCostTotal(cottonData.getMachineryCostTotal())
        .totalIrrigationCost(cottonData.getTotalIrrigationCost())
        .irrigationFrequency(cottonData.getIrrigationFrequency())
        .build();
  }

  /**
   * Get a comparator for sorting cotton farming report DTOs
   *
   * @param sortBy The field to sort by
   * @return A comparator for the specified field
   */
  private Comparator<CottonFarmingReportDto> getCottonFarmingComparator(String sortBy) {
    return switch (sortBy.toLowerCase()) {
      case "farmername" -> Comparator.comparing(CottonFarmingReportDto::getFarmerName, Comparator.nullsLast(String::compareTo));
      case "farmerid" -> Comparator.comparing(CottonFarmingReportDto::getFarmerId, Comparator.nullsLast(String::compareTo));
      case "state" -> Comparator.comparing(CottonFarmingReportDto::getState, Comparator.nullsLast(String::compareTo));
      case "district" -> Comparator.comparing(CottonFarmingReportDto::getDistrict, Comparator.nullsLast(String::compareTo));
      case "village" -> Comparator.comparing(CottonFarmingReportDto::getVillage, Comparator.nullsLast(String::compareTo));
      case "age" -> Comparator.comparing(CottonFarmingReportDto::getAge, Comparator.nullsLast(Integer::compareTo));
      case "gender" -> Comparator.comparing(CottonFarmingReportDto::getGender, Comparator.nullsLast(String::compareTo));
      case "landSizeUnderCultivation" -> Comparator.comparing(CottonFarmingReportDto::getLandSizeUnderCultivation, Comparator.nullsLast(Double::compareTo));
      case "organicStatus" -> Comparator.comparing(CottonFarmingReportDto::getOrganicStatus, Comparator.nullsLast(String::compareTo));
      case "partnerName" -> Comparator.comparing(CottonFarmingReportDto::getPartnerName, Comparator.nullsLast(String::compareTo));
      case "programName" -> Comparator.comparing(CottonFarmingReportDto::getProgramName, Comparator.nullsLast(String::compareTo));
      default -> Comparator.comparing(CottonFarmingReportDto::getFarmerId, Comparator.nullsLast(String::compareTo));
    };
  }

  /**
   * Get distinct years from cotton farming data filtered by program ID and partner ID
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @return List of distinct years in ascending order
   */
  public List<Integer> getDistinctCottonFarmingYears(Long programId, Long partnerId) {
    return cottonFarmingDataRepository.findDistinctYearsByProgramIdAndPartnerId(programId, partnerId);
  }
}
